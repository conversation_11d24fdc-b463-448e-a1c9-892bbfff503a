#!/usr/bin/env python3
"""
Local OCR using Tesseract as an alternative to Google Cloud Vision API.
This doesn't require any cloud credentials or billing.
"""
import os
import sys
from pathlib import Path

def install_tesseract():
    """Install Tesseract OCR and pytesseract."""
    print("Installing Tesseract OCR...")
    
    # Check if tesseract is already installed
    try:
        import subprocess
        result = subprocess.run(['tesseract', '--version'], capture_output=True)
        if result.returncode == 0:
            print("Tesseract is already installed")
        else:
            raise FileNotFoundError
    except FileNotFoundError:
        print("Installing Tesseract...")
        os.system("sudo apt-get update && sudo apt-get install -y tesseract-ocr")
    
    # Install pytesseract
    try:
        import pytesseract
        print("pytesseract is already installed")
    except ImportError:
        print("Installing pytesseract...")
        os.system("pip install pytesseract pillow")

def detect_text_local(image_path):
    """Detect text using local Tesseract OCR."""
    try:
        import pytesseract
        from PIL import Image
    except ImportError:
        print("Required libraries not found. Installing...")
        install_tesseract()
        import pytesseract
        from PIL import Image
    
    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found")
        return
    
    try:
        # Open image
        image = Image.open(image_path)
        
        # Extract text
        text = pytesseract.image_to_string(image)
        
        if text.strip():
            print("Detected text (Local Tesseract):")
            print("=" * 50)
            print(text)
            print("=" * 50)
        else:
            print("No text detected in the image.")
            
        # Get detailed data with bounding boxes
        data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
        
        print("\nDetailed text elements:")
        for i in range(len(data['text'])):
            if int(data['conf'][i]) > 0:  # Only show confident detections
                text_element = data['text'][i].strip()
                if text_element:
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                    confidence = data['conf'][i]
                    print(f"'{text_element}' - Bounds: ({x},{y}) to ({x+w},{y+h}) - Confidence: {confidence}%")
                    
    except Exception as e:
        print(f"Error during local OCR: {e}")

if __name__ == "__main__":
    image_path = 'jar.jpg'
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
    
    detect_text_local(image_path)
