from dataclasses import dataclass
import llm, easy_ocr
import cv2



def main():
    image_path = "jar.jpg"

    preprocessing_result: llm.PreprocessingResult = llm.run_preprocessing(image_path)
    

    #image, result = easy_ocr.run_easy_ocr(image_path, [preprocessing_result.language, "en"])
    image, result = easy_ocr.run_easy_ocr(image_path, ["cs", "en"])

    #convert the result to a list of OCRBox objects


    ocr_boxes = [llm.OCRBox(lt=[int(box[0][0][0]), int(box[0][0][1])], rb=[int(box[0][2][0]), int(box[0][2][1])], text=box[1], prob=box[2]) for box in result]

    print(result)
    print(ocr_boxes)

    postprocessing_result = llm.run_postprocessing(preprocessing_result.messages, ocr_boxes)
    print(postprocessing_result)


    for (coord, text, prob) in result:
        (top_left, top_right, bottom_right, bottom_left) = coord
        tx, ty = (int(top_left[0]), int(top_left[1]))
        bx, by = (int(bottom_right[0]), int(bottom_right[1]))
        cv2.rectangle(image, (tx, ty), (bx, by), (0, 255, 0), 2)
        cv2.putText(image, text, (tx, ty - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)


    #save the image
    cv2.imwrite("easy_ocr_result.png", image)

if __name__ == "__main__":
    main()