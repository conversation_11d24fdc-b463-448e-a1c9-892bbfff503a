#!/usr/bin/env python3
"""
Helper script to set up Google Cloud Vision API credentials.
"""
import os
import json
from pathlib import Path

def setup_credentials():
    print("Google Cloud Vision API Credential Setup")
    print("=" * 50)
    
    print("\nTo use Google Cloud Vision API, you need to:")
    print("1. Create a Google Cloud Project")
    print("2. Enable the Vision API")
    print("3. Create a Service Account")
    print("4. Download the JSON key file")
    
    print("\nDetailed steps:")
    print("1. Go to: https://console.cloud.google.com/")
    print("2. Create a new project or select existing one")
    print("3. Go to 'APIs & Services' > 'Library'")
    print("4. Search for 'Cloud Vision API' and enable it")
    print("5. Go to 'APIs & Services' > 'Credentials'")
    print("6. Click 'Create Credentials' > 'Service Account'")
    print("7. Fill in the service account details")
    print("8. <PERSON> the 'Cloud Vision API User' role")
    print("9. Create and download the JSON key file")
    
    print("\nOnce you have the JSON key file:")
    key_file = input("Enter the path to your JSON key file (or press Enter to skip): ").strip()
    
    if key_file and os.path.exists(key_file):
        # Copy the key file to the project directory
        project_key_file = "google-cloud-key.json"
        with open(key_file, 'r') as src, open(project_key_file, 'w') as dst:
            dst.write(src.read())
        
        print(f"\nKey file copied to: {project_key_file}")
        print("Setting up environment variable...")
        
        # Create a .env file
        with open('.env', 'w') as f:
            f.write(f'GOOGLE_APPLICATION_CREDENTIALS=google-cloud-key.json\n')
        
        print("Created .env file with GOOGLE_APPLICATION_CREDENTIALS")
        print("\nTo use the credentials, run:")
        print("export GOOGLE_APPLICATION_CREDENTIALS=google-cloud-key.json")
        print("or source the .env file in your shell")
        
    else:
        print("\nAlternatively, you can:")
        print("1. Place your JSON key file in this directory")
        print("2. Set the environment variable:")
        print("   export GOOGLE_APPLICATION_CREDENTIALS=path/to/your/key.json")
        print("3. Or use Application Default Credentials (gcloud auth application-default login)")

if __name__ == "__main__":
    setup_credentials()
